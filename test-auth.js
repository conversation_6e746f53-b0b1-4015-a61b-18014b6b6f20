#!/usr/bin/env node

// Simple test script to verify cookie-based authentication
// Using built-in fetch (Node.js 18+)

const BASE_URL = 'http://localhost:3001';

async function testAuth() {
  console.log('Testing cookie-based authentication...\n');

  // Test 1: Health check
  console.log('1. Testing health endpoint...');
  try {
    const response = await fetch(`${BASE_URL}/health`);
    const data = await response.json();
    console.log('✓ Health check passed:', data.status);
  } catch (error) {
    console.log('✗ Health check failed:', error.message);
    return;
  }

  // Test 2: Check if credentials: 'include' is working
  console.log('\n2. Testing credentials inclusion...');
  try {
    const response = await fetch(`${BASE_URL}/api/auth/get-session`, {
      credentials: 'include'
    });
    console.log('✓ Credentials include test - Status:', response.status);
    if (response.status === 401) {
      console.log('  (Expected 401 for unauthenticated request)');
    }
  } catch (error) {
    console.log('✗ Credentials test failed:', error.message);
  }

  // Test 3: Test API endpoint that requires authentication
  console.log('\n3. Testing protected endpoint...');
  try {
    const response = await fetch(`${BASE_URL}/v1/listings`, {
      credentials: 'include'
    });
    console.log('✓ Protected endpoint test - Status:', response.status);
    if (response.status === 401) {
      console.log('  (Expected 401 for unauthenticated request)');
    }
  } catch (error) {
    console.log('✗ Protected endpoint test failed:', error.message);
  }

  console.log('\n✓ Basic authentication flow tests completed!');
  console.log('\nNext steps:');
  console.log('1. Open http://localhost:8081 in your browser');
  console.log('2. Try to sign in with valid credentials');
  console.log('3. Check that API calls work without explicit token management');
  console.log('4. Verify session persists across browser refreshes');
}

testAuth().catch(console.error);
