# Bearer Token Authentication Fix

## Problem
The POST `/v1/listings` endpoint was failing with authentication error because the system wasn't properly managing Bearer token authentication in the 2-token strategy.

## Authentication Strategy Clarification
- **Primary Authentication**: Bearer tokens in Authorization header
- **Refresh Token**: Stored in httpOnly cookies for security
- **No Cookie-based API Authentication**: Cookies are only for refresh tokens, not for API requests

## Root Cause
The authentication flow was incorrectly falling back to cookie-based API authentication instead of ensuring proper Bearer token management with refresh token handling.

## Fixes Implemented

### 1. Bearer Token Only API Requests (`src/lib/api-client.ts`)
- Removed all `*WithCookies` methods (createListingWithCookies, updateListingWithCookies, etc.)
- Enhanced `createListing()` to require and validate access tokens
- All API requests now use Bearer token authentication exclusively
- Added proper error handling for missing tokens

### 2. Proper Token Refresh Logic (`src/contexts/AuthContext.tsx`)
- Enhanced `refreshSession()` to use refresh token from httpOnly cookie
- Improved logging to show the 2-token strategy in action
- Better error handling when refresh tokens are invalid/expired
- Clear distinction between access tokens (in memory) and refresh tokens (in cookies)

### 3. Listing Creation Mutation (`src/hooks/useQueryApi.tsx`)
- Removed cookie-based fallback for API requests
- Enhanced token refresh logic with proper error handling
- Clear error messages when authentication fails
- All mutations now require valid Bearer tokens

### 4. Updated Debug Utilities (`src/utils/auth-debug.ts`)
- Updated to focus on Bearer token + refresh token cookie strategy
- Renamed `hasSessionCookie` to `hasRefreshTokenCookie` for clarity
- Better validation of the 2-token authentication setup

### 5. Enhanced Debug Panel (`src/components/debug/AuthDebugPanel.tsx`)
- Updated to show refresh token cookie status
- Added explanation of the 2-token strategy
- Better visualization of authentication state

## Authentication Flow

### Normal Request Flow
1. User makes API request
2. System checks for access token in memory
3. If token exists, adds `Authorization: Bearer <token>` header
4. API request proceeds

### Token Refresh Flow
1. Access token is missing/expired
2. System calls `refreshSession()`
3. `refreshSession()` makes request to `/get-session` with `credentials: 'include'`
4. Backend validates refresh token from httpOnly cookie
5. Backend returns new access token
6. System stores new access token in memory
7. API request retries with new Bearer token

### Error Handling
- If refresh token is invalid/expired: User must sign in again
- If access token is missing and refresh fails: Clear error message
- No fallback to cookie-based API authentication

## Testing the Fix

### 1. Check Authentication State
```tsx
import { AuthDebugPanel } from '@/components/debug/AuthDebugPanel';

// Add to any component to monitor auth state
<AuthDebugPanel />
```

### 2. Console Logs to Monitor
- `AuthContext: Refreshing session using refresh token from httpOnly cookie...`
- `API Client: Creating listing with Bearer token`
- `🔐 Auth Debug: Creating Listing - Start`

### 3. Network Tab Verification
- API requests should have `Authorization: Bearer <token>` header
- No API requests should rely on cookies for authentication
- Refresh requests to `/get-session` should include cookies

### 4. Expected Behavior
- ✅ All API requests use Bearer tokens
- ✅ Refresh tokens stored securely in httpOnly cookies
- ✅ Automatic token refresh when needed
- ✅ Clear error messages for authentication failures
- ❌ No cookie-based API authentication fallbacks

## Files Modified
- `src/hooks/useQueryApi.tsx` - Bearer token only mutations
- `src/lib/api-client.ts` - Removed cookie-based methods
- `src/contexts/AuthContext.tsx` - Enhanced refresh logic
- `src/utils/auth-debug.ts` - Updated for 2-token strategy
- `src/components/debug/AuthDebugPanel.tsx` - Better visualization

This implementation now correctly follows the 2-token strategy with Bearer tokens for API authentication and httpOnly cookies for secure refresh token storage.
